import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Equal, In, Like, MoreThan, Repository, UpdateResult, MoreThanOrEqual, Not } from 'typeorm';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { CommonService } from 'src/middleware/common.service';
import { RedisService } from 'src/middleware/redis.service';
import { RoleEntity } from 'src/entities/role.entity';
import { GuildEntity } from 'src/entities/guild/guild.entity';
import { GuildMemberEntity } from 'src/entities/guild/guildMember.entity';
import { GuildApplicationEntity } from 'src/entities/guild/guildApplication.entity';
import { GuildHallEntity } from 'src/entities/guild/guildHall.entity';
import { TerritoryEntity } from 'src/entities/guild/territory.entity';
import { UserConfigProvider } from 'src/common/user-config.provider';

@Injectable()
// 帮派相关服务
export class GuildService {
    private readonly serviceName = 'guild'
    private readonly size = 20
    private userSetting: any

    constructor(
        private readonly commonService: CommonService,
        private eventEmitter: EventEmitter2,
        private readonly redis: RedisService,
        private readonly userConfigProvider: UserConfigProvider,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(GuildEntity) private readonly guildEntity: Repository<GuildEntity>,
        @InjectRepository(GuildMemberEntity) private readonly guildMemberEntity: Repository<GuildMemberEntity>,
        @InjectRepository(GuildApplicationEntity) private readonly guildApplicationEntity: Repository<GuildApplicationEntity>,
        @InjectRepository(GuildHallEntity) private readonly guildHallEntity: Repository<GuildHallEntity>,
        @InjectRepository(TerritoryEntity) private readonly territoryEntity: Repository<TerritoryEntity>,
        private dataSource: DataSource,
    ) {
        this.userSetting = this.userConfigProvider.getUserSetting()
    }

    // 帮派主页
    async guildMain(sid: string, cmd: number, userId: string) {
        let msg = '';
        let pageTitle = '帮派';
        const { params, urlObj, backRouter } = await this.commonService.initLink(sid, cmd, userId, this.serviceName, {}, pageTitle);
        
        // 获取用户信息
        let userInfo = await this.roleEntity.findOne({ where: { id: Number(userId) } });
        let content = '';

        if (userInfo.guildName) {
            // 已加入帮派，显示帮派信息
            let guildInfo = await this.guildEntity.findOne({ where: { name: userInfo.guildName } });
            if (guildInfo) {
                content = `${msg}【${guildInfo.name}】<br/>
                帮派等级：${guildInfo.level}<br/>
                成员数量：${guildInfo.currentMembers}/${guildInfo.maxMembers}<br/>
                帮派声望：${guildInfo.prestige}<br/>
                <a href="${this.commonService.seturlOther(params, urlObj, '帮派信息', 'guildInfo', { guildId: guildInfo.id },this.serviceName)}">帮派信息</a><br/>
                <a href="${this.commonService.seturlOther(params, urlObj, '成员列表', 'memberList', {})}">成员列表</a><br/>
                <a href="${this.commonService.seturlOther(params, urlObj, '分堂管理', 'hallList', {})}">分堂管理</a><br/>
                <a href="${this.commonService.seturlOther(params, urlObj, '领地争夺', 'territoryList', {})}">领地争夺</a><br/>
                <a href="${this.commonService.seturlOther(params, urlObj, '退出帮派', 'leaveGuild', {})}">退出帮派</a><br/>`;
            }
        } else {
            // 未加入帮派，显示加入选项
            content = `${msg}你还没有加入帮派<br/>
            <a href="${this.seturlOther(params, urlObj, '创建帮派', 'createGuild', {})}">创建帮派</a><br/>
            <a href="${this.seturlOther(params, urlObj, '帮派列表', 'guildList', {})}">加入帮派</a><br/>`;
        }

        content += backRouter;
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800);
        return content;
    }
}
